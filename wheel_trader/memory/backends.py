"""
Memory Backend Abstraction Layer

This module provides a flexible backend system for memory storage with support for:
- PgVectorBackend: Custom implementation using Supabase + pgvector (prioritized)
- Mem0Backend: Integration with mem0 library
- Optimized for fast financial market operations
"""

import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional

import openai
from supabase import Client, create_client

from wheel_trader import config

# Configure logging
logger = logging.getLogger(__name__)


class MemoryStorageError(Exception):
    """Exception raised for memory storage operations"""
    pass


class MemoryBackend(ABC):
    """Abstract base class for memory backends"""
    
    def __init__(self):
        self.health_stats = {
            "queries": 0,
            "errors": 0,
            "avg_latency": 0.0,
            "last_error": None,
            "last_query_time": None
        }
    
    @abstractmethod
    def store_memory(self, content: str, metadata: Dict[str, Any], 
                    agent_name: str, mem_type: str = "agent_action",
                    trace_id: Optional[str] = None, span_id: Optional[str] = None) -> str:
        """Store a memory and return its ID"""
        pass
    
    @abstractmethod
    def search_memories(self, query: str, agent_name: Optional[str] = None,
                       filters: Optional[Dict[str, Any]] = None,
                       limit: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant memories"""
        pass
    
    @abstractmethod
    def get_health_status(self) -> Dict[str, Any]:
        """Get backend health status"""
        pass
    
    def _update_health_stats(self, latency: float, success: bool, error: Optional[str] = None):
        """Update health statistics"""
        self.health_stats["queries"] += 1
        self.health_stats["last_query_time"] = datetime.now().isoformat()
        
        if success:
            # Update rolling average latency
            current_avg = self.health_stats["avg_latency"]
            query_count = self.health_stats["queries"]
            self.health_stats["avg_latency"] = (current_avg * (query_count - 1) + latency) / query_count
        else:
            self.health_stats["errors"] += 1
            self.health_stats["last_error"] = error


class PgVectorBackend(MemoryBackend):
    """
    Custom pgvector implementation using Supabase
    Optimized for fast financial market operations
    """
    
    def __init__(self, embedding_model: str = "text-embedding-3-small"):
        super().__init__()
        self.embedding_model = embedding_model
        self.supabase = None
        self._init_supabase()
        
        # Set OpenAI API key if available
        if config.OPENAI_API_KEY:
            openai.api_key = config.OPENAI_API_KEY
    
    def _init_supabase(self):
        """Initialize Supabase client"""
        try:
            if config.SUPABASE_URL and config.SUPABASE_KEY:
                self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
                logger.info("PgVectorBackend: Supabase client initialized")
            else:
                logger.warning("PgVectorBackend: Supabase credentials not configured")
        except Exception as e:
            logger.error(f"PgVectorBackend: Failed to initialize Supabase: {e}")
    
    def _get_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI"""
        try:
            text = text.replace("\n", " ")
            response = openai.Embedding.create(input=[text], model=self.embedding_model)
            return response['data'][0]['embedding']
        except Exception as e:
            logger.error(f"PgVectorBackend: Failed to generate embedding: {e}")
            raise MemoryStorageError(f"Failed to generate embedding: {e}")
    
    def store_memory(self, content: str, metadata: Dict[str, Any], 
                    agent_name: str, mem_type: str = "agent_action",
                    trace_id: Optional[str] = None, span_id: Optional[str] = None) -> str:
        """Store a memory in pgvector database"""
        start_time = time.time()
        
        if not self.supabase:
            raise MemoryStorageError("Supabase client not initialized")
        
        try:
            # Generate embedding
            embedding = self._get_embedding(content)
            
            # Prepare data for storage
            data = {
                "agent_name": agent_name,
                "mem_type": mem_type,
                "content": content,
                "embedding": embedding,
                "metadata": metadata,
                "importance_score": metadata.get("importance", 0.5),
                "tags": metadata.get("tags", []),
                "trace_id": trace_id,
                "span_id": span_id
            }
            
            # Add expiration if specified
            if "expires_in_hours" in metadata:
                expires_at = datetime.now() + timedelta(hours=metadata["expires_in_hours"])
                data["expires_at"] = expires_at.isoformat()
            
            # Store in database
            result = self.supabase.table("memory_embeddings").insert(data).execute()
            
            # Update health stats
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)
            
            memory_id = result.data[0]["id"]
            logger.debug(f"PgVectorBackend: Stored memory {memory_id} for agent {agent_name}")
            return memory_id
            
        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"PgVectorBackend: Failed to store memory: {e}")
            raise MemoryStorageError(f"Failed to store memory: {e}")
    
    def search_memories(self, query: str, agent_name: Optional[str] = None,
                       filters: Optional[Dict[str, Any]] = None,
                       limit: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant memories using pgvector similarity"""
        start_time = time.time()
        
        if not self.supabase:
            raise MemoryStorageError("Supabase client not initialized")
        
        try:
            # Generate query embedding
            query_embedding = self._get_embedding(query)
            
            # Prepare filters
            filters = filters or {}
            mem_type_filter = filters.get("mem_type")
            match_threshold = filters.get("match_threshold", 0.78)
            min_importance = filters.get("min_importance", 0.0)
            tag_filters = filters.get("tags")
            time_window_hours = filters.get("time_window_hours")
            include_expired = filters.get("include_expired", False)
            
            # Call the database function
            result = self.supabase.rpc(
                "match_memories",
                {
                    "query_embedding": query_embedding,
                    "agent_filter": agent_name,
                    "mem_type_filter": mem_type_filter,
                    "match_threshold": match_threshold,
                    "match_count": limit,
                    "min_importance": min_importance,
                    "tag_filters": tag_filters,
                    "time_window_hours": time_window_hours,
                    "include_expired": include_expired
                }
            ).execute()
            
            # Update health stats
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)
            
            memories = result.data or []
            logger.debug(f"PgVectorBackend: Found {len(memories)} memories for query")
            return memories
            
        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"PgVectorBackend: Failed to search memories: {e}")
            raise MemoryStorageError(f"Failed to search memories: {e}")
    
    def search_by_content(self, search_text: str, agent_name: Optional[str] = None,
                         mem_type: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Fast text-based search for exact content matches"""
        start_time = time.time()
        
        if not self.supabase:
            raise MemoryStorageError("Supabase client not initialized")
        
        try:
            result = self.supabase.rpc(
                "search_memories_by_content",
                {
                    "search_text": search_text,
                    "agent_filter": agent_name,
                    "mem_type_filter": mem_type,
                    "limit_count": limit
                }
            ).execute()
            
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)
            
            return result.data or []
            
        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"PgVectorBackend: Failed to search by content: {e}")
            raise MemoryStorageError(f"Failed to search by content: {e}")
    
    def consolidate_memories(self, agent_name: Optional[str] = None,
                           similarity_threshold: float = 0.95,
                           max_consolidations: int = 100) -> int:
        """Consolidate similar memories to reduce redundancy"""
        start_time = time.time()
        
        if not self.supabase:
            raise MemoryStorageError("Supabase client not initialized")
        
        try:
            result = self.supabase.rpc(
                "consolidate_memories",
                {
                    "similarity_threshold": similarity_threshold,
                    "agent_filter": agent_name,
                    "max_consolidations": max_consolidations
                }
            ).execute()
            
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)
            
            consolidated_count = result.data or 0
            logger.info(f"PgVectorBackend: Consolidated {consolidated_count} memories")
            return consolidated_count
            
        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"PgVectorBackend: Failed to consolidate memories: {e}")
            return 0
    
    def cleanup_expired_memories(self) -> int:
        """Remove expired memories"""
        start_time = time.time()
        
        if not self.supabase:
            raise MemoryStorageError("Supabase client not initialized")
        
        try:
            result = self.supabase.rpc("cleanup_expired_memories").execute()
            
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)
            
            deleted_count = result.data or 0
            logger.info(f"PgVectorBackend: Cleaned up {deleted_count} expired memories")
            return deleted_count
            
        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"PgVectorBackend: Failed to cleanup expired memories: {e}")
            return 0
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get backend health status"""
        try:
            # Test database connection
            if self.supabase:
                # Quick health check query
                result = self.supabase.table("memory_embeddings").select("count").limit(1).execute()
                db_healthy = True
            else:
                db_healthy = False
            
            success_rate = 1.0
            if self.health_stats["queries"] > 0:
                success_rate = (self.health_stats["queries"] - self.health_stats["errors"]) / self.health_stats["queries"]
            
            return {
                "backend_type": "pgvector",
                "healthy": db_healthy and success_rate > 0.9,
                "database_connected": db_healthy,
                "success_rate": success_rate,
                "avg_latency_ms": self.health_stats["avg_latency"] * 1000,
                "total_queries": self.health_stats["queries"],
                "total_errors": self.health_stats["errors"],
                "last_error": self.health_stats["last_error"],
                "last_query_time": self.health_stats["last_query_time"]
            }
            
        except Exception as e:
            logger.error(f"PgVectorBackend: Health check failed: {e}")
            return {
                "backend_type": "pgvector",
                "healthy": False,
                "error": str(e)
            }


class Mem0Backend(MemoryBackend):
    """
    Integration with mem0 library for managed memory services
    Provides fallback option when pgvector is not available
    """

    def __init__(self, mem0_config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.mem0_config = mem0_config or {}
        self.memory = None
        self._init_mem0()

    def _init_mem0(self):
        """Initialize mem0 memory instance"""
        try:
            from mem0 import Memory
            self.memory = Memory(self.mem0_config)
            logger.info("Mem0Backend: Memory instance initialized")
        except ImportError:
            logger.error("Mem0Backend: mem0 library not installed. Install with: pip install mem0ai")
            raise MemoryStorageError("mem0 library not installed")
        except Exception as e:
            logger.error(f"Mem0Backend: Failed to initialize mem0: {e}")
            raise MemoryStorageError(f"Failed to initialize mem0: {e}")

    def store_memory(self, content: str, metadata: Dict[str, Any],
                    agent_name: str, mem_type: str = "agent_action",
                    trace_id: Optional[str] = None, span_id: Optional[str] = None) -> str:
        """Store a memory using mem0"""
        start_time = time.time()

        if not self.memory:
            raise MemoryStorageError("Mem0 memory instance not initialized")

        try:
            # Prepare messages for mem0
            messages = [{"role": "user", "content": content}]

            # Enhance metadata with our custom fields
            enhanced_metadata = {
                **metadata,
                "mem_type": mem_type,
                "trace_id": trace_id,
                "span_id": span_id,
                "created_at": datetime.now().isoformat()
            }

            # Store using mem0
            result = self.memory.add(messages, user_id=agent_name, metadata=enhanced_metadata)

            # Update health stats
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)

            memory_id = result.get("id", "unknown")
            logger.debug(f"Mem0Backend: Stored memory {memory_id} for agent {agent_name}")
            return memory_id

        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"Mem0Backend: Failed to store memory: {e}")
            raise MemoryStorageError(f"Failed to store memory via mem0: {e}")

    def search_memories(self, query: str, agent_name: Optional[str] = None,
                       filters: Optional[Dict[str, Any]] = None,
                       limit: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant memories using mem0"""
        start_time = time.time()

        if not self.memory:
            raise MemoryStorageError("Mem0 memory instance not initialized")

        try:
            # Use mem0's search functionality
            result = self.memory.search(
                query=query,
                user_id=agent_name,
                limit=limit
            )

            # Update health stats
            latency = time.time() - start_time
            self._update_health_stats(latency, success=True)

            # Convert mem0 results to our standard format
            memories = []
            for item in result.get("results", []):
                memory = {
                    "id": item.get("id"),
                    "content": item.get("memory", ""),
                    "metadata": item.get("metadata", {}),
                    "similarity": item.get("score", 0.0),
                    "agent_name": agent_name,
                    "mem_type": item.get("metadata", {}).get("mem_type", "agent_action"),
                    "created_at": item.get("metadata", {}).get("created_at"),
                    "trace_id": item.get("metadata", {}).get("trace_id"),
                    "span_id": item.get("metadata", {}).get("span_id")
                }
                memories.append(memory)

            logger.debug(f"Mem0Backend: Found {len(memories)} memories for query")
            return memories

        except Exception as e:
            latency = time.time() - start_time
            self._update_health_stats(latency, success=False, error=str(e))
            logger.error(f"Mem0Backend: Failed to search memories: {e}")
            raise MemoryStorageError(f"Failed to search memories via mem0: {e}")

    def get_health_status(self) -> Dict[str, Any]:
        """Get backend health status"""
        try:
            # Test mem0 connection by attempting a simple operation
            if self.memory:
                # Try to get user memories (this will test the connection)
                test_result = self.memory.get_all(user_id="health_check", limit=1)
                mem0_healthy = True
            else:
                mem0_healthy = False

            success_rate = 1.0
            if self.health_stats["queries"] > 0:
                success_rate = (self.health_stats["queries"] - self.health_stats["errors"]) / self.health_stats["queries"]

            return {
                "backend_type": "mem0",
                "healthy": mem0_healthy and success_rate > 0.9,
                "mem0_connected": mem0_healthy,
                "success_rate": success_rate,
                "avg_latency_ms": self.health_stats["avg_latency"] * 1000,
                "total_queries": self.health_stats["queries"],
                "total_errors": self.health_stats["errors"],
                "last_error": self.health_stats["last_error"],
                "last_query_time": self.health_stats["last_query_time"]
            }

        except Exception as e:
            logger.error(f"Mem0Backend: Health check failed: {e}")
            return {
                "backend_type": "mem0",
                "healthy": False,
                "error": str(e)
            }


def create_memory_backend(backend_type: str = "pgvector",
                         config: Optional[Dict[str, Any]] = None) -> MemoryBackend:
    """
    Factory function to create memory backend instances

    Args:
        backend_type: Type of backend ("pgvector" or "mem0")
        config: Backend-specific configuration

    Returns:
        MemoryBackend instance
    """
    config = config or {}

    if backend_type == "pgvector":
        embedding_model = config.get("embedding_model", "text-embedding-3-small")
        return PgVectorBackend(embedding_model=embedding_model)

    elif backend_type == "mem0":
        mem0_config = config.get("mem0_config", {})
        return Mem0Backend(mem0_config=mem0_config)

    else:
        raise ValueError(f"Unsupported backend type: {backend_type}. Use 'pgvector' or 'mem0'")


# Export main classes and functions
__all__ = [
    "MemoryBackend",
    "PgVectorBackend",
    "Mem0Backend",
    "MemoryStorageError",
    "create_memory_backend"
]
