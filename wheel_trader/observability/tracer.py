"""
Enhanced Phoenix Tracer for Options Trading Agents

Comprehensive tracing system optimized for financial market operations with
memory integration, performance monitoring, and agent health tracking.
"""

import time
import logging
from functools import wraps
from typing import Any, Dict, Optional, List, Union
from datetime import datetime
from contextlib import contextmanager

import phoenix as px
from phoenix.trace import trace

# Configure logging
logger = logging.getLogger(__name__)


class AgentTracer:
    """
    Enhanced Phoenix tracing for trading agents with memory integration
    Optimized for fast financial market operations
    """
    
    def __init__(self, agent_name: str, session_name: Optional[str] = None):
        """
        Initialize agent tracer
        
        Args:
            agent_name: Name of the agent being traced
            session_name: Optional session name for grouping traces
        """
        self.agent_name = agent_name
        self.session_name = session_name or f"{agent_name}_session"
        
        # Initialize Phoenix session
        try:
            self.session = px.Session()
            logger.info(f"AgentTracer: Initialized Phoenix session for {agent_name}")
        except Exception as e:
            logger.warning(f"AgentTracer: Failed to initialize Phoenix session: {e}")
            self.session = None
        
        # Tracing statistics
        self.stats = {
            "traces_created": 0,
            "traces_completed": 0,
            "traces_failed": 0,
            "total_duration_ms": 0,
            "last_trace_time": None
        }
    
    @contextmanager
    def trace_execution(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """
        Context manager for tracing agent operations
        
        Args:
            operation_name: Name of the operation being traced
            metadata: Additional metadata to include in the trace
        """
        trace_name = f"{self.agent_name}.{operation_name}"
        start_time = time.time()
        
        # Prepare trace metadata
        trace_metadata = {
            "agent_name": self.agent_name,
            "session_name": self.session_name,
            "operation": operation_name,
            "start_time": datetime.now().isoformat(),
            "market_context": "options_trading"
        }
        
        if metadata:
            trace_metadata.update(metadata)
        
        self.stats["traces_created"] += 1
        
        try:
            with trace(name=trace_name, metadata=trace_metadata) as span:
                # Set initial span attributes
                span.set_attribute("agent.name", self.agent_name)
                span.set_attribute("operation.name", operation_name)
                span.set_attribute("session.name", self.session_name)
                
                yield span
                
                # Mark as successful
                duration_ms = int((time.time() - start_time) * 1000)
                span.set_attribute("duration_ms", duration_ms)
                span.set_attribute("success", True)
                
                # Update statistics
                self.stats["traces_completed"] += 1
                self.stats["total_duration_ms"] += duration_ms
                self.stats["last_trace_time"] = datetime.now().isoformat()
                
        except Exception as e:
            # Handle trace failures
            duration_ms = int((time.time() - start_time) * 1000)
            self.stats["traces_failed"] += 1
            self.stats["total_duration_ms"] += duration_ms
            
            logger.error(f"AgentTracer: Trace failed for {trace_name}: {e}")
            raise
    
    def trace_memory_operation(self, operation: str, query: Optional[str] = None, 
                             result_count: Optional[int] = None):
        """
        Context manager for tracing memory operations
        
        Args:
            operation: Memory operation type (store, search, consolidate, etc.)
            query: Search query for search operations
            result_count: Number of results for search operations
        """
        metadata = {
            "memory_operation": operation,
            "component": "memory_system"
        }
        
        if query:
            metadata["query"] = query
        if result_count is not None:
            metadata["result_count"] = result_count
        
        return self.trace_execution(f"memory.{operation}", metadata)
    
    def trace_tool_execution(self, tool_name: str, tool_input: Dict[str, Any]):
        """
        Context manager for tracing tool executions
        
        Args:
            tool_name: Name of the tool being executed
            tool_input: Input parameters for the tool
        """
        metadata = {
            "tool_name": tool_name,
            "tool_input": str(tool_input)[:500],  # Limit input size
            "component": "tool_execution"
        }
        
        return self.trace_execution(f"tool.{tool_name}", metadata)
    
    def trace_market_analysis(self, symbol: str, analysis_type: str):
        """
        Context manager for tracing market analysis operations
        
        Args:
            symbol: Stock/option symbol being analyzed
            analysis_type: Type of analysis (technical, fundamental, options, etc.)
        """
        metadata = {
            "symbol": symbol,
            "analysis_type": analysis_type,
            "component": "market_analysis",
            "market_session": self._get_market_session()
        }
        
        return self.trace_execution(f"analysis.{analysis_type}", metadata)
    
    def trace_trading_decision(self, symbol: str, action: str, quantity: Optional[int] = None):
        """
        Context manager for tracing trading decisions
        
        Args:
            symbol: Symbol being traded
            action: Trading action (buy, sell, hold, etc.)
            quantity: Number of shares/contracts
        """
        metadata = {
            "symbol": symbol,
            "action": action,
            "component": "trading_decision",
            "market_session": self._get_market_session()
        }
        
        if quantity:
            metadata["quantity"] = quantity
        
        return self.trace_execution(f"trading.{action}", metadata)
    
    def trace_health_check(self, health_score: float, components: List[str]):
        """
        Context manager for tracing agent health checks
        
        Args:
            health_score: Current health score
            components: List of components being checked
        """
        metadata = {
            "health_score": health_score,
            "components_checked": components,
            "component": "health_monitoring"
        }
        
        return self.trace_execution("health.check", metadata)
    
    def _get_market_session(self) -> str:
        """Get current market session (pre-market, regular, after-hours, closed)"""
        now = datetime.now()
        hour = now.hour
        
        # Simplified market session detection (EST)
        if 4 <= hour < 9:
            return "pre_market"
        elif 9 <= hour < 16:
            return "regular_hours"
        elif 16 <= hour < 20:
            return "after_hours"
        else:
            return "closed"
    
    def get_trace_stats(self) -> Dict[str, Any]:
        """Get tracing statistics"""
        total_traces = self.stats["traces_created"]
        avg_duration = 0
        
        if self.stats["traces_completed"] > 0:
            avg_duration = self.stats["total_duration_ms"] / self.stats["traces_completed"]
        
        success_rate = 0
        if total_traces > 0:
            success_rate = self.stats["traces_completed"] / total_traces
        
        return {
            "agent_name": self.agent_name,
            "session_name": self.session_name,
            "traces_created": total_traces,
            "traces_completed": self.stats["traces_completed"],
            "traces_failed": self.stats["traces_failed"],
            "success_rate": success_rate,
            "avg_duration_ms": avg_duration,
            "total_duration_ms": self.stats["total_duration_ms"],
            "last_trace_time": self.stats["last_trace_time"],
            "phoenix_session_active": self.session is not None
        }


def trace_execution(operation_name: Optional[str] = None, 
                   include_memory: bool = True,
                   include_performance: bool = True):
    """
    Decorator for automatic execution tracing
    
    Args:
        operation_name: Custom operation name (defaults to function name)
        include_memory: Whether to include memory context
        include_performance: Whether to include performance metrics
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get tracer from self if available
            tracer = getattr(self, 'tracer', None)
            if not tracer:
                # Fallback to basic execution
                return func(self, *args, **kwargs)
            
            # Determine operation name
            op_name = operation_name or func.__name__
            
            # Prepare metadata
            metadata = {
                "function_name": func.__name__,
                "module": func.__module__,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys())
            }
            
            # Add performance context
            if include_performance:
                metadata["performance_tracking"] = True
            
            # Execute with tracing
            with tracer.trace_execution(op_name, metadata) as span:
                start_time = time.time()
                
                try:
                    result = func(self, *args, **kwargs)
                    
                    # Add result metadata
                    if hasattr(result, '__len__'):
                        span.set_attribute("result_size", len(result))
                    
                    span.set_attribute("execution_time_ms", int((time.time() - start_time) * 1000))
                    
                    return result
                    
                except Exception as e:
                    span.set_attribute("error_type", type(e).__name__)
                    span.set_attribute("error_message", str(e))
                    span.set_attribute("execution_time_ms", int((time.time() - start_time) * 1000))
                    raise
        
        return wrapper
    return decorator


def trace_memory_operation(operation_type: str):
    """
    Decorator for tracing memory operations
    
    Args:
        operation_type: Type of memory operation (store, search, etc.)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            tracer = getattr(self, 'tracer', None)
            if not tracer:
                return func(self, *args, **kwargs)
            
            # Extract query from args/kwargs for search operations
            query = None
            if operation_type == "search" and args:
                query = str(args[0])[:100]  # Limit query length
            
            with tracer.trace_memory_operation(operation_type, query) as span:
                start_time = time.time()
                
                try:
                    result = func(self, *args, **kwargs)
                    
                    # Add result metadata for search operations
                    if operation_type == "search" and hasattr(result, '__len__'):
                        span.set_attribute("results_found", len(result))
                    
                    span.set_attribute("memory_operation_time_ms", int((time.time() - start_time) * 1000))
                    
                    return result
                    
                except Exception as e:
                    span.set_attribute("memory_error", str(e))
                    span.set_attribute("memory_operation_time_ms", int((time.time() - start_time) * 1000))
                    raise
        
        return wrapper
    return decorator


# Export main classes and functions
__all__ = [
    "AgentTracer",
    "trace_execution", 
    "trace_memory_operation"
]
